using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainDetailForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBuffTip;
        [SerializeField] private UIButton m_btnMask;
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnPlunder;
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnBattleRecord;

        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtPower;
        [SerializeField] private UIText m_txtTodayPlunderCount;
        [SerializeField] private UIText m_txtTime;
        [SerializeField] private UIText m_txtBuffTipTitle;
        [SerializeField] private UIText m_txtBuffTipContent;
        [SerializeField] private UIText m_txtBuffTipLock;
        [SerializeField] private UIText m_txtTip;

        [SerializeField] private Transform m_transCarriage;
        [SerializeField] private Transform m_transRewardParent;
        [SerializeField] private Transform m_transReward;
        [SerializeField] private Transform m_transTeam;
        [SerializeField] private GameObject m_goMyPlunder;
        [SerializeField] private Transform m_transBuffItem;
        [SerializeField] private Transform m_transBuff;
        [SerializeField] private GameObject m_goBuffTip;
        [SerializeField] private GameObject m_goBuffTipArrow;
        [SerializeField] private GameObject m_goTip;
        [SerializeField] private GameObject m_goItemEffect;

        void InitBind()
        {
            m_btnBuffTip.onClick.AddListener(OnBtnBuffTipClick);
            m_btnMask.onClick.AddListener(OnBtnMaskClick);
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnPlunder.onClick.AddListener(OnBtnPlunderClick);
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnBattleRecord.onClick.AddListener(OnBtnBattleRecordClick);
        }
    }
}
