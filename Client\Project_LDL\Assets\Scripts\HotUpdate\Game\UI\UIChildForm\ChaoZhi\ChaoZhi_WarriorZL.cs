using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using Mosframe;
using Spine.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class ChaoZhi_WarriorZL : SwitchPanelLogic
    {
        [SerializeField] private UIText m_txtTitle; //标题
        [SerializeField] private UIText m_txtTimer; //倒计时文本
        [SerializeField] private SkeletonGraphic m_spuiRole; //活动spine
        [SerializeField] private UIText m_txtDiscount; //购买折扣
        [SerializeField] private UIText m_txtDesc; //高级奖励描述
        [SerializeField] private UIButton m_btnHelp; //说明按钮
        [SerializeField] private UIButton m_btnBuy; //购买按钮
        [SerializeField] private UIButton m_btnCheck; //英雄查看跳转
        [SerializeField] private UIImage m_imgScore; //积分图标
        [SerializeField] private UIText m_txtScore; //积分数量

        [SerializeField] private UISwitchTagGroup m_TagGroup; //标签节点

        [SerializeField] private GameObject m_goRewardNode; //奖励部分
        [SerializeField] private GameObject m_goDailyTaskNode; //每日任务部分
        [SerializeField] private GameObject m_goTargetTaskNode; //目标任务部分

        [SerializeField] private UIButton m_btnGetAll; //一键领取按钮

        //预制体
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goTagItem;
        [SerializeField] private GameObject m_goTaskItem;
        [SerializeField] private TableViewV m_ScoreTableView;
        [SerializeField] private RectTransform cycleContent;
        [SerializeField] private ScrollRect cycleScroll;
        [SerializeField] private Slider m_sliderProgress;
        [SerializeField] private ScrollRect sliderScroll;
        [SerializeField] private GameObject m_goScoreRewardItem;
        [SerializeField] private GameObject m_goTaskReward;

        //每日任务列表
        public TableViewV m_DailyTableViewV;

        //目标任务列表
        public TableViewV m_TargetTableViewV;

        private bool isTimer = false;
        private int curIndex = -1;
        private string iconPath;
        private int timerCount; //倒计时数值

        private ActivityTime activityMsg;
        protected int TemplateId;
        private PushActivityBattlePassConfig ConfigData;
        private PushActivityBattlePassData MsgData;
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        protected float startRatio = 0;

        [SerializeField] private GameObject m_goInfiniteBox;

        private List<ActivityBattlePassScore> scoreConfig = new List<ActivityBattlePassScore>();
        private List<ActivityBattlePassTask> dailyTaskList = new List<ActivityBattlePassTask>();
        private List<ActivityBattlePassTask> TargetTaskList = new List<ActivityBattlePassTask>();

        private List<activity_battlepass_task> TasksConfig = new List<activity_battlepass_task>();

        private itemid iconId;
        private List<int> uniqueIdList = new List<int>();

        private WaitForSeconds delayTime = new WaitForSeconds(0.2f);

        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            startRatio = 0.016f;
            scoreConfig.Clear();
            dailyTaskList.Clear();
            TargetTaskList.Clear();

            ToolScriptExtend.GetTable<activity_battlepass_task>(out TasksConfig);

            var bindData = GetBindData();
            if (bindData != null)
            {
                if (bindData is ActivityTime data)
                {
                    activityMsg = data;
                    ChaoZhiManager.C2SLoadActivityInfo(activityMsg);
                }
            }

            m_ScoreTableView.GetItemCount = () =>
            {
                if (ConfigData == null || ConfigData.ScoreRewards == null)
                {
                    return 0;
                }
                else
                {
                    return ConfigData.ScoreRewards.Count;
                }
            };
            m_ScoreTableView.GetItemGo = () => m_goScoreRewardItem;
            m_ScoreTableView.UpdateItemCell = UpdateScoreLogic;
            m_ScoreTableView.InitTableViewByIndex(0);

            //给循环滑动事件添加逻辑，让sliderScroll跟随循环列表滑动
            cycleScroll.onValueChanged.AddListener((a) => { sliderScroll.normalizedPosition = a; });

            //每日任务
            m_DailyTableViewV.GetItemCount = () => { return dailyTaskList == null ? 0 : dailyTaskList.Count; };
            m_DailyTableViewV.GetItemGo = () => m_goTaskItem;
            m_DailyTableViewV.UpdateItemCell = ((i, o) => { UpdateTaskLogic(i, o, refreshtype.refresh_type_daily); });
            m_DailyTableViewV.InitTableViewByIndex(0);

            //目标任务
            m_TargetTableViewV.GetItemCount = () => { return TargetTaskList == null ? 0 : TargetTaskList.Count; };
            m_TargetTableViewV.GetItemGo = () => m_goTaskItem;
            m_TargetTableViewV.UpdateItemCell = ((i, o) =>
            {
                UpdateTaskLogic(i, o, refreshtype.refresh_type_no_refresh);
            });
            m_TargetTableViewV.InitTableViewByIndex(0);

            var nameList = new List<string>()
            {
                ToolScriptExtend.GetLang(80100032), //"奖励", 
                ToolScriptExtend.GetLang(1100267), //每日任务
                ToolScriptExtend.GetLang(1100413), //"目标任务"
            };
            m_TagGroup.Init(m_goTagItem, m_TagGroup.transform, nameList, (index) => { OnSwitchTagLogic(index); });
            uniqueIdList.Clear();
            uniqueIdList.AddRange(new List<int>() { 1, 2, 3 });
            m_TagGroup.BindUniqueId(uniqueIdList);

            //高级奖励描述
            m_txtDesc.text = ToolScriptExtend.GetLang(1100412);

            //一键领取按钮
            BindBtnLogic(m_btnGetAll, () =>
            {
                //奖励页签领奖
                var rewardCount = ChaoZhiManager.CheckWarriorZLRewardDot(TemplateId);
                //每日任务+目标任务 领奖
                var taskCount = ChaoZhiManager.CheckWarriorZLAllTaskDot(TemplateId);
                if (rewardCount + taskCount == 0)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1100426) //目前没有领取的奖励
                    });
                    return;
                }

                var reqData = new ActivityDrawReq();
                reqData.Type = activityMsg.Type;
                reqData.Template = MsgData.Template;
                reqData.LoopTimes = MsgData.LoopTimes;
                reqData.DrawId = 1;
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });

            CheckMultiLanguage(gameObject);
            
        }

        //再次打开
        public override void OnReOpen()
        {
            if (m_TagGroup != null)
            {
                m_TagGroup.ForceSelect(0);
            }
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
        }

        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "ChaoZhi_WarriorZL")
            {
                //1活动信息更新
                if (param == 1)
                {
                    TemplateId = (int)activityMsg.Template;
                    ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
                    MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
                    iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                    iconPath = ToolScriptExtend.GetItemIcon(iconId);
                    InitConfigPageView();
                    InitPageView();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                }
            }
        }

        //资源释放
        public override void Release()
        {
            scoreConfig.Clear();
            dailyTaskList.Clear();
            TargetTaskList.Clear();
        }

        private void OnSwitchTagLogic(int index)
        {
            m_goRewardNode.SetActive(index == 0);
            m_goDailyTaskNode.SetActive(index == 1);
            m_goTargetTaskNode.SetActive(index == 2);

            if (index == 0)
            {
                ScrollToRewardIndex();
            }
        }

        private void InitConfigPageView()
        {
            var TemplateId = (int)MsgData.Template;
            if (!ToolScriptExtend.GetTable<activity_main>(out var table)) return;
            var activityMain = table.FirstOrDefault(x => (int)x.activity_template == TemplateId);
            if (activityMain == null) return;
            //活动标题
            m_txtTitle.text = ToolScriptExtend.GetLang(activityMain.name);

            //说明按钮
            BindBtnLogic(m_btnHelp,
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, activityMain.activity_explain); });
            if (!ToolScriptExtend.GetTable<activity_battlepass_main>(out var table1)) return;
            var config = table1.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
            if (config == null) return;

            var heroId = (int)config.hero_image;
            //活动spine
            ToolScriptExtend.ShowHeroSpine(heroId, m_spuiRole);
            //积分信息：图标
            m_imgScore.SetImage(ToolScriptExtend.GetItemIcon(iconId));
            //查看英雄跳转
            BindBtnLogic(m_btnCheck, () =>
            {
                var module = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
                if (module.IsCombind)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroForm);
                }
                else
                {
                    GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroId);
                }
            });
            
            if (ToolScriptExtend.GetTable<activity_battlepass_grade>(out var table2))
            {
                var gradeConfig = table2.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
                if (gradeConfig != null)
                {
                    //全购状态  性价比  原价  折扣价  累充积分
                    m_txtDiscount.text = $"{gradeConfig.effectiveness}%";
                    GameEntry.LogicData.MallData.CreateRechargeScore(gradeConfig.payment_id, m_btnBuy.transform, 40,
                        -90);
                    if (ToolScriptExtend.GetConfigById<payment>((int)gradeConfig.payment_id, out var data1))
                    {
                        var priceTxt = m_btnBuy.transform.Find("Text").GetComponent<UIText>();
                        priceTxt.text = ToolScriptExtend.GetLang(data1.price_lang_id);

                        //购买按钮
                        BindBtnLogic(m_btnBuy,
                            () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm, TemplateId); });
                    }
                }
            }
        }

        private void InitPageView()
        {
            var TemplateId = (int)MsgData.Template;
            scoreConfig = ConfigData.ScoreRewards.ToList();
            dailyTaskList = ChaoZhiManager.GetZLTaskList(TemplateId, refreshtype.refresh_type_daily);
            TargetTaskList = ChaoZhiManager.GetZLTaskList(TemplateId, refreshtype.refresh_type_no_refresh);

            //活动倒计时
            timerCount = ChaoZhiManager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);

            //积分信息数量
            m_txtScore.text = MsgData.Score.ToString();

            //购买状态显示
            var isSoldOut = IsSoldOut(new[] { 1 });
            m_txtDesc.gameObject.SetActive(!isSoldOut);
            m_btnBuy.gameObject.SetActive(!isSoldOut);

            //切页标签组
            //切页标签——奖励列表
            ShowMainReward();

            //切页标签——每日任务
            ShowDailyTask();
            //切页标签——目标任务
            ShowTargetTask();
            //无限宝箱
            ShowInfiniteBox();

            CalculateProgress();

            ShowLittleTagRedDot();
        }

        //每日任务
        private void ShowDailyTask()
        {
            if (m_DailyTableViewV.itemPrototype == null)
            {
                m_DailyTableViewV.InitTableViewByIndex(0);
            }
            else
            {
                m_DailyTableViewV.ReloadData();
            }
        }

        //目标任务
        private void ShowTargetTask()
        {
            if (m_TargetTableViewV.itemPrototype == null)
            {
                m_TargetTableViewV.InitTableViewByIndex(0);
            }
            else
            {
                m_TargetTableViewV.ReloadData();
            }
        }

        //无限宝箱
        private void ShowInfiniteBox()
        {
            var root = m_goInfiniteBox.transform;
            var m_txtInfiniteBoxDesc = root.Find("m_txtInfiniteBoxDesc").GetComponent<UIText>();
            var lockObj = root.Find("Lock");
            var m_sliderInfinity = root.Find("m_sliderInfinity").GetComponent<Slider>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnGray = root.Find("btnGray").GetComponent<UIButton>();
            var btnCheck = root.Find("box/btnCheck").GetComponent<UIButton>();
            var m_txtInfinityValue = root.Find("m_sliderInfinity/m_txtInfinityValue").GetComponent<UIText>();
            var tip = root.Find("box/tip");
            var tipCount = root.Find("box/tip/txt").GetComponent<UIText>();

            var maxValue = ConfigData.BoxUnlockScore;

            var isUnlock = MsgData.Score >= maxValue;
            lockObj.gameObject.SetActive(!isUnlock);
            m_sliderInfinity.gameObject.SetActive(isUnlock);

            m_txtInfiniteBoxDesc.text = isUnlock
                ? ToolScriptExtend.GetLang(1100418)
                : //开启需累积战令积分
                ToolScriptExtend.GetLangFormat(1100416, maxValue.ToString()); //战令分数达到{0}后解锁

            var rewards = new List<reward>();
            foreach (var reward in ConfigData.BoxRewards)
            {
                rewards.Add(new reward() { item_id = (itemid)reward.ItemId, num = reward.Num });
            }

            ToolScriptExtend.BindBtnLogic(btnCheck,
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardPreviewForm, rewards); });

            bool isOk = false;
            if (isUnlock)
            {
                long availableValue = MsgData.Score;
                var sum = maxValue + ConfigData.BoxScore * MsgData.BoxDrawTimes;
                if (MsgData.Score >= sum)
                {
                    availableValue = MsgData.Score - sum;
                }

                var configValue = ConfigData.BoxScore;
                var showCount = availableValue % configValue;
                m_txtInfinityValue.text = $"{showCount}/{configValue}";
                var rewardCount = availableValue / configValue; //可领取数量
                var progress = showCount * 1.0f / configValue;
                m_sliderInfinity.value = Mathf.Clamp(progress, 0, 1);
                isOk = rewardCount > 0;
                tipCount.text = rewardCount.ToString();
            }

            tip.gameObject.SetActive(isUnlock && isOk);
            btnGet.gameObject.SetActive(isUnlock && isOk);
            btnGray.gameObject.SetActive(isUnlock && !isOk);

            BindBtnLogic(btnGet, () =>
            {
                var reqData = new ActivityDrawReq();
                reqData.Type = activityMsg.Type;
                reqData.Template = MsgData.Template;
                reqData.LoopTimes = MsgData.LoopTimes;
                reqData.DrawId = 0;
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });
        }

        //是否售罄,即是否可购买
        //gradeList：有一档购买填{1} 有两档购买填{1,2} 
        private bool IsSoldOut(int[] gradeArray)
        {
            if (gradeArray == null || gradeArray.Length == 0)
            {
                return false;
            }

            var list = MsgData.UnlockGrade.ToList();
            foreach (var value in gradeArray)
            {
                if (!list.Contains(value))
                {
                    return false;
                }
            }

            return true;
        }

        #region 奖励页签

        //奖励列表
        private void ShowMainReward()
        {
            if (m_ScoreTableView.itemPrototype == null)
            {
                m_ScoreTableView.InitTableViewByIndex(0);
            }
            else
            {
                m_ScoreTableView.ReloadData();
            }

            ScrollToRewardIndex();

            //统一进度条、滑动条Scroll的Content高度，和循环列表的Content高度一样
            AutoFitContent();
        }

        protected void UpdateScoreLogic(int index, GameObject obj)
        {
            var scoreData = scoreConfig[index];
            var id = scoreData.Id;
            var root = obj.transform.Find("main");
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnReGet = root.Find("btnReGet").GetComponent<UIButton>();
            var btnLock = root.Find("btnLock").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish").gameObject;
            var scoreIcon = root.Find("scoreIcon").GetComponent<UIImage>();
            var scoreTxt = root.Find("scoreIcon/scoreTxt").GetComponent<UIText>();

            var freeReward = root.Find("freeReward");
            var payReward1 = root.Find("payReward1");
            var payReward2 = root.Find("payReward2");

            var scoreValue = scoreData.Score;
            var status = ChaoZhiManager.CheckZLItemStatus(id, scoreValue,MsgData.Score,TemplateId);
            //奖励领取状态 0：未解锁 1:可领取 2：已领取 3：继续领取 
            btnLock.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            btnReGet.gameObject.SetActive(status == 3);

            var hex = status == 0 ? "#FFFFFF" : "#3AF416";
            scoreTxt.text = $"<color={hex}>{scoreValue}</color>";

            m_imgScore.SetImage(ToolScriptExtend.GetItemIcon(iconId));

            if (!string.IsNullOrEmpty(iconPath))
            {
                scoreIcon.SetImage(iconPath);
            }

            //奖励
            //免费奖励
            var reward1 = scoreData.Reward0.ToList().First();
            ChaoZhiManager.ShowZLRewardMsg(id, true, freeReward, reward1, scoreValue,m_goReward,TemplateId);

            //付费第一档奖励
            var temp = scoreData.Reward1.ToList();
            ChaoZhiManager.ShowZLRewardMsg(id, false, payReward1, temp[0], scoreValue,m_goReward,TemplateId);
            ChaoZhiManager.ShowZLRewardMsg(id, false, payReward2, temp[1], scoreValue,m_goReward,TemplateId);

            BindBtnLogic(btnGet, () =>
            {
                var reqData = new ActivityDrawReq();
                reqData.Type = activityMsg.Type;
                reqData.Template = MsgData.Template;
                reqData.LoopTimes = MsgData.LoopTimes;
                reqData.DrawId = scoreData.Id;
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });

            BindBtnLogic(btnReGet,
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm, (int)MsgData.Template); });
        }
        
        //列表滑动到第一个可领取奖励的位置上,或者进行中的节点item
        private void ScrollToRewardIndex()
        {
            if (ConfigData == null || MsgData == null)
            {
                return;
            }

            var dataList = ConfigData.ScoreRewards.ToList();
            List<ActivityBattlePassScore> sortList = new List<ActivityBattlePassScore>();
            sortList.AddRange(ConfigData.ScoreRewards);
            sortList.Sort((a, b) =>
            {
                var statusA = ChaoZhiManager.CheckZLItemStatus(a.Id, a.Score,MsgData.Score,TemplateId);
                var statusB = ChaoZhiManager.CheckZLItemStatus(b.Id, b.Score,MsgData.Score,TemplateId);
                var flagA = GetRewardCompareFlag(statusA);
                var flagB = GetRewardCompareFlag(statusB);
                if (flagA != flagB) return flagA - flagB;
                return a.Score - b.Score;
            });

            var result = sortList[0];
            var resultScore = result.Score;
            var resultStatus = ChaoZhiManager.CheckZLItemStatus(result.Id, result.Score,MsgData.Score,TemplateId);
            var index = dataList.FindIndex(x => x.Score == resultScore);
            if (resultStatus == 0)
            {
                if (index - 1 < 0)
                {
                    index = 0;
                }
                else
                {
                    index--;
                }
            }

            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(DelayScroll(() =>
                {
                    m_ScoreTableView.scrollByItemIndex(index);
                    sliderScroll.normalizedPosition = cycleScroll.normalizedPosition;
                }));
            }
        }

        IEnumerator DelayScroll(Action callback)
        {
            yield return delayTime;
            callback?.Invoke();
        }

        // 自定义奖励排序优先级 :可领取(1),未解锁(2),继续领取(3),已领取(4),
        private int GetRewardCompareFlag(int status)
        {
            // 积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取 3：继续领取 
            if (status == 0)
            {
                return 2;
            }
            else if (status == 1)
            {
                return 1;
            }
            else if (status == 2)
            {
                return 4;
            }
            else if (status == 3)
            {
                return 3;
            }

            return 4;
        }

        #endregion

        #region 任务标签

        public void UpdateTaskLogic(int index, GameObject obj, refreshtype type)
        {
            ActivityBattlePassTask data = null;
            if (type == refreshtype.refresh_type_daily)
            {
                data = dailyTaskList[index];
            }
            else if (type == refreshtype.refresh_type_no_refresh)
            {
                data = TargetTaskList[index];
            }

            if (data == null) return;

            if (TasksConfig == null) return;
            var config = TasksConfig.FirstOrDefault(x => x.task_id == data.Id);
            if (config == null) return;

            var root = obj.transform.Find("bg");
            var test = obj.transform.Find("test").GetComponent<UIText>();
            var name = root.Find("name").GetComponent<UIText>();
            var btnGo = root.Find("btnGo").GetComponent<UIButton>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish");
            var btnReGet = root.Find("btnReGet").GetComponent<UIButton>();
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var progress = root.Find("name/progress").GetComponent<UIText>();

            var content = ChaoZhiManager.GetTaskFormatStr(config.activity_task_desc,config.task_type,config.task_value);
            var result = $"({data.Process}/{config.task_target_value})";
            var sumStr = content + result;
            test.text = sumStr;
            LayoutRebuilder.ForceRebuildLayoutImmediate(test.rectTransform);
            var width = test.rectTransform.rect.width;
            var compareValue = 960;
            var nameOffset = 0;
            if (width >= compareValue)
            {
                if (width is > 960 and <= 1100)
                {
                    nameOffset = 5;
                }
                else if (width is > 1100 and <= 1200)
                {
                    nameOffset = 8;
                }
                else if (width is > 1200 and <= 1300)
                {
                    nameOffset = 10;
                }
                else
                {
                    nameOffset = 14;
                    name.fontSize = 24;
                }
            }

            progress.fontSize = 40 - nameOffset;
            name.fontSize = 38 - nameOffset;
            name.text = content;
            progress.text = result;
            LayoutRebuilder.ForceRebuildLayoutImmediate(name.rectTransform);
            
            List<reward> rewardList = config.activity_task_reward;
            ToolScriptExtend.RecycleOrCreate(m_goTaskReward, rewardRoot, rewardList.Count);

            var rewards = new List<PbGameconfig.reward>();
            foreach (var node in rewardList)
            {
                rewards.Add(new PbGameconfig.reward() { ItemId = (PbGameconfig.itemid)node.item_id, Num = node.num });
            }

            ChaoZhiManager.ShowRewardList(m_goReward, rewardRoot, rewards, 0.56f);
            scroll.enabled = rewardList.Count > 4;

            //奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
            var status = ChaoZhiManager.GetZLTaskStatus(data);
            btnGo.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            btnReGet.gameObject.SetActive(status == 3);

            BindBtnLogic(btnGo, () => { });
            BindBtnLogic(btnGet, () =>
            {
                var req = new ActivityBattlePassDrawTaskReq
                {
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    TaskId = data.Id
                };
                ChaoZhiManager.C2SActivityBattlePassDrawTaskReq(req, (resp) =>
                {
                    var iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                    var temp = config.activity_task_reward.FirstOrDefault(x => x.item_id == iconId);
                    if (temp != null)
                    {
                        FlyResManager.UIFlyByTrans(iconId, (int)temp.num, btnGet.transform, m_imgScore.transform);
                    }
                });
            });
            BindBtnLogic(btnReGet, () =>
            {
                var isPay = ChaoZhiManager.IsWarriorZLVipPay(TemplateId, 1);
                if (isPay)
                {
                    var req = new ActivityBattlePassDrawTaskReq
                    {
                        Template = MsgData.Template,
                        LoopTimes = MsgData.LoopTimes,
                        TaskId = data.Id
                    };
                    ChaoZhiManager.C2SActivityBattlePassDrawTaskReq(req, (resp) =>
                    {
                        var iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                        var temp = config.activity_task_reward.FirstOrDefault(x => x.item_id == iconId);
                        if (temp != null)
                        {
                            FlyResManager.UIFlyByTrans(iconId, (int)temp.num, btnGet.transform, m_imgScore.transform);
                        }
                    });
                }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm, TemplateId);
                }
            });
        }

        // //列表滑动到第一个可领取奖励的位置上,或者进行中的节点item
        // private void ScrollToTaskIndex(refreshtype type)
        // {
        //     if (ConfigData == null || MsgData == null)
        //     {
        //         return;
        //     }
        //     var dataList = ChaoZhiManager.GetZLTaskList(TemplateId,type);
        //     List<ActivityBattlePassTask> sortList = new List<ActivityBattlePassTask>();
        //     sortList.AddRange(dataList);
        //     sortList.Sort((a, b) =>
        //     {
        //         var statusA = ChaoZhiManager.GetZLTaskStatus(a);
        //         var statusB = ChaoZhiManager.GetZLTaskStatus(b);
        //         var flagA = GetTaskCompareFlag(statusA);
        //         var flagB = GetTaskCompareFlag(statusB);
        //         if (flagA != flagB) return flagA - flagB;
        //         return a.Id - b.Id;
        //     });
        //     var result = sortList[0].Id;
        //     var index = dataList.FindIndex(x => x.Id == result);
        //     
        //     if (gameObject.activeInHierarchy)
        //     {
        //         StartCoroutine(DelayScroll(() =>
        //         {
        //             if (type == refreshtype.refresh_type_daily)
        //             {
        //                 
        //                 m_DailyTableViewV.scrollByItemIndex(index);
        //             }
        //             else if (type == refreshtype.refresh_type_no_refresh)
        //             {
        //                 
        //                 m_TargetTableViewV.scrollByItemIndex(index);
        //             }
        //         }));
        //     }
        // }

        // 自定义奖励排序优先级 :可领取(1),继续领取(2),未解锁(3),已领取(4),
        private int GetTaskCompareFlag(int status)
        {
            //奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
            if (status == 0)
            {
                return 3;
            }
            else if (status == 1)
            {
                return 1;
            }
            else if (status == 2)
            {
                return 4;
            }
            else if (status == 3)
            {
                return 2;
            }

            return 4;
        }

        #endregion


        private void AutoFitContent()
        {
            //统一进度条、滑动条Scroll的Content高度，和循环列表的Content高度一样
            var itemRect = m_sliderProgress.GetComponent<RectTransform>();
            var calculateHeight = cycleContent.rect.height;
            itemRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, calculateHeight - 100);
            sliderScroll.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, calculateHeight);
        }

        //计算进度值
        private void CalculateProgress()
        {
            var scoreList = ConfigData.ScoreRewards.Select(x => x.Score).ToList();
            scoreList.Sort((a, b) => (int)a - (int)b);
            var maxValue = scoreList.Last();
            if (MsgData.Score >= maxValue)
            {
                m_sliderProgress.value = 1;
            }
            else
            {
                var list1 = new List<int>();
                foreach (var score in scoreList)
                {
                    list1.Add((int)score);
                }

                var ratioList = new List<float>();
                var nodeList = new List<int>();
                var lastValue = 0;

                var checkCount = scoreList.Count;
                var unit = (1 - startRatio) / (checkCount - 1);
                for (var i = 0; i < checkCount; i++)
                {
                    ratioList.Add(i == 0 ? startRatio : unit);
                }

                for (var i = 0; i < checkCount; i++)
                {
                    if (i == 0)
                    {
                        ratioList.Add(unit * 1.0f / 2);
                    }
                    else
                    {
                        ratioList.Add(unit);
                    }
                }

                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    var offset = curScore - lastValue;
                    nodeList.Add(offset);
                    lastValue = curScore;
                }

                var value = MsgData.Score;
                float ratioSum = 0;
                int offsetSum = 0;
                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    if (value > curScore)
                    {
                        ratioSum += ratioList[i];
                        offsetSum = curScore;
                    }
                    else
                    {
                        var finalOffset = value - offsetSum;
                        ratioSum += (finalOffset * 1.0f / nodeList[i]) * ratioList[i];
                        break;
                    }
                }

                m_sliderProgress.value = ratioSum;
            }
        }

        //更新显示内部小切页标签的红点
        private void ShowLittleTagRedDot()
        {
            var sum = 0;
            foreach (var id in uniqueIdList)
            {
                var count = 0;
                if (id == 1)
                {
                    count = ChaoZhiManager.CheckWarriorZLRewardDot(TemplateId);
                }
                else if (id == 2)
                {
                    count = ChaoZhiManager.CheckWarriorZLDailyDot(TemplateId);
                }
                else if (id == 3)
                {
                    count = ChaoZhiManager.CheckWarriorZLTargetDot(TemplateId);
                }

                sum += count;
                m_TagGroup.CheckNumDotLogic(id, count);
            }

            var getAllDot = m_btnGetAll.transform.Find("dot");
            getAllDot.gameObject.SetActive(sum > 0);
        }
    }
}