using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTruckDetailForm : UGuiFormEx
    {
        Trade.TradeVanDetailResp truckDetail;
        Trade.TradeVanRecord truckRecord;

        UIImage imgCollect;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            imgCollect = m_btnCollect.GetComponent<UIImage>();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UITradeTruckDetailFormParams param)
            {
                truckDetail = param.truckDetail;
                truckRecord = param.truckRecord;
                switch (param.type)
                {
                    case TruckDetailType.History:
                        m_goTitle.SetActive(false);
                        m_goBubble.SetActive(true);
                        break;
                    case TruckDetailType.Plunder:
                        m_goTitle.SetActive(true);
                        m_goBubble.SetActive(false);
                        m_txtTitle.text = ToolScriptExtend.GetLangFormat(1224, "1h");
                        ToolScriptExtend.AdjustLineSpacing(m_txtTitle);
                        m_txtContent.text = ToolScriptExtend.GetLang(1225);
                        ToolScriptExtend.AdjustLineSpacing(m_txtContent);
                        break;
                    case TruckDetailType.Reward:
                        m_goTitle.SetActive(true);
                        m_goBubble.SetActive(false);
                        m_txtTitle.text = ToolScriptExtend.GetLang(1227);
                        ToolScriptExtend.AdjustLineSpacing(m_txtTitle);
                        m_txtContent.text = ToolScriptExtend.GetLang(1228);
                        ToolScriptExtend.AdjustLineSpacing(m_txtContent);
                        break;
                }
                RefreshPanel();

                if (truckDetail != null)
                {
                    if (truckDetail.Record != null && truckDetail.Record.Count > 0)
                    {
                        RefreshBtnCollect(truckDetail.Record[0].Collect);
                    }
                    else
                    {
                        RefreshBtnCollect(false);
                    }
                }
                else if (truckRecord != null)
                {
                    RefreshBtnCollect(truckRecord.Collect);
                }
            }

            m_txtSubTitle.text = ToolScriptExtend.GetLang(1209);
            m_goBattle.transform.Find("item/bg_1/bg2/Text").GetComponent<UIText>().text = ToolScriptExtend.GetLang(1138) + "19.8M";
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnCollectClick()
        {
            if (truckDetail != null)
            {
                if (truckDetail.Record != null && truckDetail.Record.Count > 0)
                {
                    Trade.TradeVanRecord tradeVanRecord = truckDetail.Record[0];
                    bool isCollect = tradeVanRecord.Collect;
                    ulong roleId = truckDetail.CargoTransport.RoleId;
                    uint serverId = truckDetail.CargoTransport.ServerId;
                    ColorLog.Pink("货车是否收藏", !isCollect, "roleId", roleId, "serverId", serverId);
                    GameEntry.TradeTruckData.RequestTruckCollect(tradeVanRecord.Id, !isCollect, roleId, serverId, (result) =>
                    {
                        tradeVanRecord.Collect = !isCollect;
                        RefreshBtnCollect(tradeVanRecord.Collect);
                    });
                }
            }
            else if (truckRecord != null)
            {
                bool isCollect = truckRecord.Collect;
                ulong roleId = GameEntry.RoleData.RoleID;
                uint serverId = GameEntry.RoleData.ServerId;
                ColorLog.Pink("货车是否收藏", !isCollect, "roleId", roleId, "serverId", serverId);
                GameEntry.TradeTruckData.RequestTruckCollect(truckRecord.Id, !isCollect, roleId, serverId, (result) =>
                {
                    truckRecord.Collect = !isCollect;
                    RefreshBtnCollect(truckRecord.Collect);
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckHistoryForm, TruckHistoryRefreshType.CollectRefresh);
                });
            }
        }

        private void OnBtnShareClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
        }

        void RefreshPanel()
        {
            if (truckDetail != null)
            {
                Trade.TradeCargoTransport truckData = truckDetail.CargoTransport;
                if (truckData == null) return;

                trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckData.TradeId);
                car_quality quality = truckConfig.car_quality;
                m_imgTruckIcon.SetImage(GetTruckIcon(quality));
                m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
                if (truckData.Boxcar.Count > 0)
                {
                    List<Trade.TradeGoods> rewards = new(truckData.Boxcar[0].Goods);
                    RefreshReward(rewards);
                }
                Team.TeamType teamType = Team.TeamType.TradeVanDefend1;
                if (truckData.Teams.Count > 0)
                {
                    teamType = truckData.Teams[0].Type;
                }
                RefreshTeam(teamType);
            }
            else if (truckRecord != null)
            {
                trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckRecord.TradeId);
                car_quality quality = truckConfig.car_quality;
                m_imgTruckIcon.SetImage(GetTruckIcon(quality));
                m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
                if (truckRecord.Goods.Count > 0)
                {
                    List<Trade.TradeGoods> rewards = new(truckRecord.Goods);
                    RefreshReward(rewards);
                }
            }
        }

        void RefreshReward(List<Trade.TradeGoods> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            rewards.Sort((a, b) => a.Rob.CompareTo(b.Rob));

            for (int i = 0; i < rewards.Count; i++)
            {
                bool rob = rewards[i].Rob;
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                    uiItemModule.plunder.SetActive(rob);
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.75f);
                        item.plunder.SetActive(rob);
                    });
                }
            }
        }

        void RefreshTeam(Team.TeamType teamType)
        {
            List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);
            for (int i = 0; i < 5; i++)
            {
                Transform transHeroItem;
                if (i < m_transContentHero.childCount)
                {
                    transHeroItem = m_transContentHero.GetChild(i);
                }
                else
                {
                    transHeroItem = Instantiate(m_transHeroItem, m_transContentHero);
                }
                transHeroItem.gameObject.SetActive(true);
                GameObject empty = transHeroItem.Find("empty").gameObject;
                UIHeroItem item = transHeroItem.Find("UIHeroItem").GetComponent<UIHeroItem>();
                if (teamData != null && i < teamData.Count)
                {
                    HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)teamData[i].HeroId);
                    item.Refresh(heroModule);
                    item.gameObject.SetActive(true);
                    empty.SetActive(false);
                }
                else
                {
                    item.gameObject.SetActive(false);
                    empty.SetActive(true);
                }
            }
        }

        void RefreshBtnCollect(bool isCollect)
        {
            if (isCollect)
            {
                imgCollect.SetImage("Sprite/ui_public/button6_shoucang1.png");
            }
            else
            {
                imgCollect.SetImage("Sprite/ui_public/button6_shoucang.png");
            }
        }

        string GetTruckIcon(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_dis_car1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_dis_car2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_dis_car3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_dis_car4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_dis_car5.png",
                _ => "Sprite/ui_maoyi/maoyi_dis_car1.png",
            };
        }
    }
}
