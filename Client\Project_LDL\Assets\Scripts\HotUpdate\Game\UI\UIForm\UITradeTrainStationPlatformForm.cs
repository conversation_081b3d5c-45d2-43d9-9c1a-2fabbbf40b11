using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using GameFramework.Resource;
using GameFramework.Event;
using Mosframe;

namespace Game.Hotfix
{
    public partial class UITradeTrainStationPlatformForm : UGuiFormEx
    {
        Dictionary<string, string> buffIcons = new()
        {
            {"1_0", "Sprite/ui_maoyi/maoyi_kapian_icon6.png"},
            {"1_1", "Sprite/ui_maoyi/maoyi_kapian_icon6_1.png"},
            {"2_0", "Sprite/ui_maoyi/maoyi_kapian_icon7.png"},
            {"2_1", "Sprite/ui_maoyi/maoyi_kapian_icon7_1.png"},
            {"3_0", "Sprite/ui_maoyi/maoyi_kapian_icon8.png"},
            {"3_1", "Sprite/ui_maoyi/maoyi_kapian_icon8_1.png"},
            {"4_0", "Sprite/ui_maoyi/maoyi_kapian_icon9.png"},
            {"4_1", "Sprite/ui_maoyi/maoyi_kapian_icon9_1.png"},
        };

        PageViewVertical pageView;
        GameObject curPlayer;
        GameObject curBtnRide;
        bool isRiding;
        bool isThanks;
        bool isConductor;
        bool isGuardianAngel;
        TrainStatus curTrainStatus;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();

            pageView = m_scrollview.GetComponent<PageViewVertical>();
            pageView.CreatePageView(2);
            pageView.OnPageChanged = OnPageChanged;

            UIButton btn = m_goPlayerTrainHead.transform.GetChild(0).GetComponent<UIButton>();
            btn.onClick.AddListener(() =>
            {
                Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIPlayerInfoForm);
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            ResizeBg();
            pageView.pageTo(1, false);
            curTrainStatus = GameEntry.TradeTruckData.GetTrainStatus();

            if (GameEntry.TradeTruckData.myTrain != null)
            {
                GameEntry.TradeTruckData.RequestTrainDetail(GameEntry.TradeTruckData.myTrain.Id, (result) =>
                {
                    ColorLog.Pink("查询火车详情", result);
                    CheckConductor();
                    RefreshPanel();
                });
            }

            m_btnThumbsUpAngel.gameObject.SetActive(!GameEntry.TradeTruckData.HasThumbsUpAngel);

            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            if (userData is bool flag)
            {
                isThanks = flag;
            }
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (GameEntry.TradeTruckData.myTrain == null) return;
            if (GameEntry.TradeTruckData.myTrain.Train == null) return;
            long remainTime = GameEntry.TradeTruckData.myTrain.Train.PrepareTime - (long)TimeComponent.Now;
            if (remainTime < 0)
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays(0);
            }
            else
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
            }

            TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            if (trainStatus == TrainStatus.LineUp || trainStatus == TrainStatus.Prepare)
            {

            }
            else
            {
                Close();
            }

            if (curTrainStatus == TrainStatus.LineUp && trainStatus == TrainStatus.Prepare)
            {
                RefreshCarriage();
                curTrainStatus = TrainStatus.Prepare;
            }
        }

        void OnItemChange(object sender, GameEventArgs e)
        {
            RefreshContractCount();
        }

        void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {

            }

            RefreshTeam();

            if (GameEntry.TradeTruckData.myTrain == null) return;

            if (isConductor || isGuardianAngel)
            {
                List<Trade.TradeFormationTeam> tradeFormationTeams = new()
                {
                    new Trade.TradeFormationTeam()
                    {
                        RoleId = GameEntry.RoleData.RoleID,
                        ServerId = GameEntry.RoleData.ServerId,
                        Type = Team.TeamType.TradeTrainDefend1
                    },
                    new Trade.TradeFormationTeam()
                    {
                        RoleId = GameEntry.RoleData.RoleID,
                        ServerId = GameEntry.RoleData.ServerId,
                        Type = Team.TeamType.TradeTrainDefend2
                    },
                    new Trade.TradeFormationTeam()
                    {
                        RoleId = GameEntry.RoleData.RoleID,
                        ServerId = GameEntry.RoleData.ServerId,
                        Type = Team.TeamType.TradeTrainDefend3
                    }
                };
                GameEntry.TradeTruckData.RequestTrainFormation(tradeFormationTeams, GameEntry.TradeTruckData.myTrain.Id, (result) =>
                {
                    ColorLog.Pink("火车调整布阵", result);
                });
            }
        }

        private void OnBtnTrainHeadClick()
        {
            var unionData = GameEntry.LogicData.UnionData;
            unionData.OnReqUnionMemberList(unionData.UnionId, (resp) =>
            {
                if (resp != null)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainInviteConductorForm, resp);
                }
            });
        }

        private void OnBtnVIPClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainVIPForm);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainDescForm);
        }

        private void OnBtnTopPageClick()
        {
            pageView.pageTo(0);
        }

        private void OnBtnThumbsUpAngelClick()
        {
            if (GameEntry.TradeTruckData.myTrain == null) return;
            GameEntry.TradeTruckData.RequestTrainGuardAngelLike(GameEntry.TradeTruckData.myTrain.Id, (result) =>
            {
                ColorLog.Pink("点赞守护天使", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100456)
                });
                m_btnThumbsUpAngel.gameObject.SetActive(false);
                GameEntry.TradeTruckData.HasThumbsUpAngel = true;
            });
        }

        private void OnBtnRefreshGoodsClick()
        {
            trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
            if (setting == null) return;
            itemid costID = setting.train_refeshcost.item_id;
            long costNum = setting.train_refeshcost.num;
            long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
            if (curNum < costNum)
            {
                ItemModule itemModule = new(costID);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                return;
            }

            if (GameEntry.TradeTruckData.myTrain == null) return;

            GameEntry.TradeTruckData.RequestTrainRefreshGoods(GameEntry.TradeTruckData.myTrain.Id, (result) =>
            {
                ColorLog.Pink("刷新火车货物", result);
                RefreshPanel();
            });
        }

        private void OnBtnThanksListClick()
        {
            if (GameEntry.TradeTruckData.myTrain == null) return;
            GameEntry.TradeTruckData.RequestTrainThanksList(GameEntry.TradeTruckData.myTrain.Id, (result) =>
            {
                ColorLog.Pink("火车感谢列表", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainThanksListForm, result);
            });
        }

        private void OnBtnMaskClick()
        {
            m_goBuffTip.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
            m_btnBuffTip.gameObject.SetActive(false);
            m_goTip.SetActive(false);
        }

        private void OnBtnBuffTipClick()
        {
            m_btnBuffTip.gameObject.SetActive(false);
            m_goTip.SetActive(false);
        }

        void OnPageChanged(int index, GameObject obj)
        {
            if (index == 0)
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(1300);
                m_goGuardianAngel.SetActive(true);
                m_btnTopPage.gameObject.SetActive(false);
                m_transRewardParent.GetChild(0).gameObject.SetActive(true);
                m_transBuff.gameObject.SetActive(true);
                m_goWelcome.SetActive(false);
                m_btnRefreshGoods.gameObject.SetActive(false);
                m_btnThanksList.gameObject.SetActive(false);

                Transform team = m_rectTrainGuard.GetChild(0);
                foreach (Transform item in team)
                {
                    UIText txtPower = item.Find("txtPower").GetComponent<UIText>();
                    txtPower.gameObject.SetActive(!txtPower.text.Equals("0"));

                    UIText txtLevel1 = item.Find("model1/txtLevel").GetComponent<UIText>();
                    bool isShow1 = !txtLevel1.text.Equals(string.Empty);
                    txtLevel1.gameObject.SetActive(isShow1);
                    item.Find("model1/starObj").gameObject.SetActive(isShow1);

                    UIText txtLevel2 = item.Find("model2/txtLevel").GetComponent<UIText>();
                    bool isShow2 = !txtLevel2.text.Equals(string.Empty);
                    txtLevel2.gameObject.SetActive(isShow2);
                    item.Find("model2/starObj").gameObject.SetActive(isShow2);

                    UIText txtLevel3 = item.Find("model3/txtLevel").GetComponent<UIText>();
                    bool isShow3 = !txtLevel3.text.Equals(string.Empty);
                    txtLevel3.gameObject.SetActive(isShow3);
                    item.Find("model3/starObj").gameObject.SetActive(isShow3);

                    UIText txtLevel4 = item.Find("model4/txtLevel").GetComponent<UIText>();
                    bool isShow4 = !txtLevel4.text.Equals(string.Empty);
                    txtLevel4.gameObject.SetActive(isShow4);
                    item.Find("model4/starObj").gameObject.SetActive(isShow4);

                    UIText txtLevel5 = item.Find("model5/txtLevel").GetComponent<UIText>();
                    bool isShow5 = !txtLevel5.text.Equals(string.Empty);
                    txtLevel5.gameObject.SetActive(isShow5);
                    item.Find("model5/starObj").gameObject.SetActive(isShow5);
                }
            }
            else
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(1299);
                m_goGuardianAngel.SetActive(false);
                m_btnTopPage.gameObject.SetActive(true);
                m_transRewardParent.GetChild(0).gameObject.SetActive(false);
                m_transBuff.gameObject.SetActive(false);
                m_goWelcome.SetActive(true);
                m_btnRefreshGoods.gameObject.SetActive(isConductor);
                m_btnThanksList.gameObject.SetActive(isConductor);

                Transform team = m_rectTrainGuard.GetChild(0);
                foreach (Transform item in team)
                {
                    item.Find("txtPower").gameObject.SetActive(false);
                    item.Find("model1/txtLevel").gameObject.SetActive(false);
                    item.Find("model1/starObj").gameObject.SetActive(false);
                    item.Find("model2/txtLevel").gameObject.SetActive(false);
                    item.Find("model2/starObj").gameObject.SetActive(false);
                    item.Find("model3/txtLevel").gameObject.SetActive(false);
                    item.Find("model3/starObj").gameObject.SetActive(false);
                    item.Find("model4/txtLevel").gameObject.SetActive(false);
                    item.Find("model4/starObj").gameObject.SetActive(false);
                    item.Find("model5/txtLevel").gameObject.SetActive(false);
                    item.Find("model5/starObj").gameObject.SetActive(false);
                }
            }
        }

        void InitPanel()
        {
            foreach (Transform item in m_transCarriage)
            {
                int index = int.Parse(item.gameObject.name.Substring(item.gameObject.name.Length - 1, 1));
                GameObject player = item.Find("player").gameObject;

                UIText txtPassenger = item.Find("btnPassenger/txtPassenger").GetComponent<UIText>();
                if (GameEntry.TradeTruckData.myTrain != null)
                {
                    if (index < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                    {
                        List<Trade.TradePassenger> tradePassenger = new(GameEntry.TradeTruckData.myTrain.Boxcar[index].Passengers);
                        int count = tradePassenger.Count;
                        txtPassenger.text = $"{count}/5";

                        UIButton btnPassenger = item.Find("btnPassenger").GetComponent<UIButton>();
                        btnPassenger.onClick.RemoveAllListeners();
                        btnPassenger.onClick.AddListener(() =>
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainPassengerForm, tradePassenger);
                        });
                    }
                }

                UIButton btnRide = item.Find("btnRide").GetComponent<UIButton>();
                if (isConductor)
                {
                    if (GameEntry.TradeTruckData.myTrain != null)
                    {
                        if (index < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                        {
                            List<Trade.TradePassenger> tradePassenger = new(GameEntry.TradeTruckData.myTrain.Boxcar[index].Passengers);
                            btnRide.onClick.RemoveAllListeners();
                            btnRide.onClick.AddListener(() =>
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                                {
                                    Content = ToolScriptExtend.GetLang(1100471)
                                });
                            });
                        }
                    }
                }
                else
                {
                    btnRide.onClick.RemoveAllListeners();
                    btnRide.onClick.AddListener(() =>
                    {
                        if (isConductor) return;
                        if (isRiding)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                            {
                                Title = ToolScriptExtend.GetLang(1100147),
                                Content = ToolScriptExtend.GetLang(1274),
                                ConfirmText = ToolScriptExtend.GetLang(1100144),
                                CancelText = ToolScriptExtend.GetLang(1100143),
                                OnClickConfirm = (data) =>
                                {
                                    if (curPlayer != null)
                                    {
                                        curPlayer.SetActive(false);
                                    }
                                    if (curBtnRide != null)
                                    {
                                        curBtnRide.SetActive(true);
                                    }
                                    player.SetActive(true);
                                    btnRide.gameObject.SetActive(false);
                                    curPlayer = player;
                                    curBtnRide = btnRide.gameObject;

                                    if (!isThanks)
                                    {
                                        GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainThanksForm, true);
                                    }

                                    if (GameEntry.TradeTruckData.myTrain != null)
                                    {
                                        ColorLog.Pink("排队上车", index);
                                        GameEntry.TradeTruckData.RequestTrainLineUp(index, GameEntry.TradeTruckData.myTrain.Id, (result) =>
                                        {
                                            ColorLog.Pink("排队上车回调", result);
                                            GameEntry.TradeTruckData.RequestTrainDetail(GameEntry.TradeTruckData.myTrain.Id, (result) =>
                                            {
                                                ColorLog.Pink("查询火车详情", result);
                                                CheckConductor();
                                                RefreshPanel();
                                                GameEntry.Event.Fire(TrainPassengerEventArgs.EventId, TrainPassengerEventArgs.Create());
                                            });
                                        });
                                    }
                                },
                            });
                        }
                        else
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainThanksForm, false);
                            isRiding = true;
                            if (curPlayer != null)
                            {
                                curPlayer.SetActive(false);
                            }
                            if (curBtnRide != null)
                            {
                                curBtnRide.SetActive(true);
                            }
                            player.SetActive(true);
                            btnRide.gameObject.SetActive(false);
                            curPlayer = player;
                            curBtnRide = btnRide.gameObject;

                            if (GameEntry.TradeTruckData.myTrain != null)
                            {
                                ColorLog.Pink("排队上车", index);
                                GameEntry.TradeTruckData.RequestTrainLineUp(index, GameEntry.TradeTruckData.myTrain.Id, (result) =>
                                {
                                    ColorLog.Pink("排队上车回调", result);
                                    GameEntry.TradeTruckData.RequestTrainDetail(GameEntry.TradeTruckData.myTrain.Id, (result) =>
                                    {
                                        ColorLog.Pink("查询火车详情", result);
                                        CheckConductor();
                                        RefreshPanel();
                                        GameEntry.Event.Fire(TrainPassengerEventArgs.EventId, TrainPassengerEventArgs.Create());
                                    });
                                });
                            }
                        }
                    });
                }
            }
        }

        void RefreshPanel()
        {
            RefreshContractCount();
            RefreshCarriage();
            RefreshTeam();
            RefreshBuff();
            if (GameEntry.TradeTruckData.myTrain == null) return;

            int index = 0;
            foreach (Transform item in m_transRewardParent)
            {
                if (index < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                {
                    Trade.TradeBoxcar tradeBoxcar = GameEntry.TradeTruckData.myTrain.Boxcar[index];
                    if (tradeBoxcar != null)
                    {
                        if (tradeBoxcar.Goods.Count > 0)
                        {
                            Transform grid = item.Find("grid");
                            List<Trade.TradeGoods> rewards = new(tradeBoxcar.Goods);
                            bool needEffect = index > 0;
                            RefreshReward(rewards, grid, m_transReward, needEffect);
                        }
                        UIImage border = item.GetComponent<UIImage>();
                        border.SetImage(GetBorder(tradeBoxcar.Quality));
                    }
                }
                index++;
            }

            Trade.TradeBoxcar tradeBoxcarHead = GameEntry.TradeTruckData.myTrain.Boxcar[0];
            if (tradeBoxcarHead != null && tradeBoxcarHead.Passengers.Count > 0)
            {
                Trade.TradePassenger tradePassenger = tradeBoxcarHead.Passengers[0];
                GameEntry.RoleData.RequestRoleQueryLocalSingle(tradePassenger.RoleId, (roleBrief) =>
                {
                    ColorLog.Pink("查询列车长信息", roleBrief);
                    if (roleBrief != null)
                    {
                        UIText txtLevel = m_goPlayerInfoHead.transform.Find("txtLevel").GetComponent<UIText>();
                        UIText txtName = m_goPlayerInfoHead.transform.Find("txtName").GetComponent<UIText>();
                        UIText txtPower = m_goPlayerInfoHead.transform.Find("txtPower").GetComponent<UIText>();
                        txtLevel.text = $"Level.{roleBrief.Level}";
                        txtName.text = roleBrief.Name;
                        txtPower.text = ToolScriptExtend.FormatNumberWithSeparator(roleBrief.Power);
                    }
                });
                m_goPlayerTrainHead.SetActive(true);
                m_goBottomTip.SetActive(false);
            }

            if (isConductor)
            {
                m_btnTrainHead.gameObject.SetActive(false);
                m_btnRefreshGoods.gameObject.SetActive(true);
                m_btnThanksList.gameObject.SetActive(true);
                m_goPlayerInfoHead.SetActive(false);
                // bool isShowVIP = GameEntry.LogicData.UnionData.Level >= 20;
                m_btnVIP.gameObject.SetActive(true);
            }
            else
            {
                m_btnTrainHead.gameObject.SetActive(true);
                m_btnRefreshGoods.gameObject.SetActive(false);
                m_btnThanksList.gameObject.SetActive(false);
                m_goPlayerInfoHead.SetActive(true);
                m_btnVIP.gameObject.SetActive(false);
            }
        }

        void RefreshReward(List<Trade.TradeGoods> rewards, Transform transContentReward, Transform transReward, bool needEffect = true)
        {
            foreach (Transform item in transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < transContentReward.childCount)
                {
                    transContentReward.GetChild(i).gameObject.SetActive(true);
                    if (transContentReward.GetChild(i).childCount > 0)
                    {
                        UIItemModule uiItemModule = transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                        if (uiItemModule != null)
                        {
                            uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                            uiItemModule.InitConfigData();
                            uiItemModule.DisplayInfo();
                        }
                    }
                }
                else
                {
                    int j = i;
                    Transform transRewardItem = Instantiate(transReward, transContentReward);
                    BagManager.CreatItem(transRewardItem, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.49f);
                        if (j == 0 && needEffect)
                        {
                            GameObject effect = Instantiate(m_goItemEffect, transRewardItem);
                            effect.SetActive(true);
                        }
                    });
                }
            }
        }

        void RefreshContractCount()
        {
            trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
            if (setting == null) return;
            itemid costID = setting.train_refeshcost.item_id;
            long costNum = setting.train_refeshcost.num;
            long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
            m_txtContractCount.text = $"{curNum}/{costNum}";
            if (curNum < costNum)
            {
                ColorUtility.TryParseHtmlString("#f53d3d", out Color color);
                m_txtContractCount.color = color;
            }
            else
            {
                ColorUtility.TryParseHtmlString("#60EF86", out Color color);
                m_txtContractCount.color = color;
            }
        }

        void CheckConductor()
        {
            isConductor = false;
            if (GameEntry.TradeTruckData.myTrain == null) return;
            Trade.TradeBoxcar tradeBoxcarHead = GameEntry.TradeTruckData.myTrain.Boxcar[0];
            if (tradeBoxcarHead != null && tradeBoxcarHead.Passengers.Count > 0)
            {
                isConductor = tradeBoxcarHead.Passengers[0].RoleId == GameEntry.RoleData.RoleID;
            }
            else
            {
                isConductor = false;
            }
        }

        void RefreshCarriage()
        {
            foreach (Transform item in m_transCarriage)
            {
                int index = int.Parse(item.gameObject.name.Substring(item.gameObject.name.Length - 1, 1));
                GameObject player = item.Find("player").gameObject;

                UIText txtPassenger = item.Find("btnPassenger/txtPassenger").GetComponent<UIText>();
                UIButton btnRide = item.Find("btnRide").GetComponent<UIButton>();

                TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
                btnRide.gameObject.SetActive(trainStatus == TrainStatus.LineUp);

                if (GameEntry.TradeTruckData.myTrain != null)
                {
                    if (index < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                    {
                        List<Trade.TradePassenger> tradePassenger = new(GameEntry.TradeTruckData.myTrain.Boxcar[index].Passengers);
                        int count = tradePassenger.Count;
                        txtPassenger.text = $"{count}/5";

                        UIButton btnPassenger = item.Find("btnPassenger").GetComponent<UIButton>();
                        btnPassenger.onClick.RemoveAllListeners();
                        btnPassenger.onClick.AddListener(() =>
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainPassengerForm, tradePassenger);
                        });

                        if (isConductor)
                        {
                            btnRide.onClick.RemoveAllListeners();
                            btnRide.onClick.AddListener(() =>
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                                {
                                    Content = ToolScriptExtend.GetLang(1100471)
                                });
                            });
                        }

                        foreach (var passenger in tradePassenger)
                        {
                            if (passenger.RoleId == GameEntry.RoleData.RoleID)
                            {
                                if (curPlayer != null)
                                {
                                    curPlayer.SetActive(false);
                                }
                                if (curBtnRide != null)
                                {
                                    curBtnRide.SetActive(true);
                                }
                                player.SetActive(true);
                                btnRide.gameObject.SetActive(false);
                                curPlayer = player;
                                curBtnRide = btnRide.gameObject;
                            }
                        }
                    }
                }
            }
        }

        void RefreshTeam()
        {
            Transform teamParent = m_rectTrainGuard.GetChild(0);

            foreach (Transform item in teamParent)
            {
                int index = int.Parse(item.gameObject.name.Substring(item.gameObject.name.Length - 1, 1));
                Team.TeamType teamType = Team.TeamType.TradeTrainDefend1;
                if (index == 1) teamType = Team.TeamType.TradeTrainDefend1;
                else if (index == 2) teamType = Team.TeamType.TradeTrainDefend2;
                else if (index == 3) teamType = Team.TeamType.TradeTrainDefend3;

                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);

                UIText txtPower = item.Find("txtPower").GetComponent<UIText>();
                txtPower.gameObject.SetActive(false);
                txtPower.text = string.Empty;

                if (teamData != null)
                {
                    ulong powerTotal = 0;
                    foreach (var data in teamData)
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)data.HeroId);
                        powerTotal += heroVo.power;
                    }
                    txtPower.text = ToolScriptExtend.FormatNumberWithUnit(powerTotal);
                    txtPower.gameObject.SetActive(powerTotal > 0);
                }

                for (int i = 0; i < 5; i++)
                {
                    Transform modelParent = item.Find("model" + (i + 1));
                    Transform model = modelParent.Find("model");
                    UIText txtLevel = modelParent.Find("txtLevel").GetComponent<UIText>();
                    Transform starObj = modelParent.Find("starObj");
                    List<UIImage> starList = new();
                    for (int j = 0; j < starObj.childCount; j++)
                    {
                        starList.Add(starObj.GetChild(j).GetComponent<UIImage>());
                    }

                    txtLevel.gameObject.SetActive(false);
                    starObj.gameObject.SetActive(false);

                    txtLevel.text = string.Empty;

                    if (teamData != null)
                    {
                        Fight.FormationHero heroData = null;
                        foreach (var hero in teamData)
                        {
                            if (hero.Pos == i + 1)
                            {
                                heroData = hero;
                            }
                        }

                        if (heroData != null)
                        {
                            var battleRole = GameEntry.LDLTable.GetTableById<battle_role>((int)heroData.HeroId);

                            if (model.transform.childCount > 0)
                            {
                                Destroy(model.transform.GetChild(0).gameObject);
                            }

                            GameEntry.Resource.LoadAsset(battleRole.res_location, new LoadAssetCallbacks(
                            (assetName, asset, duration, userData) =>
                            {
                                var prefab = asset as GameObject;
                                GameObject heroGo = Instantiate(prefab, model);
                                heroGo.setLayer(5);
                                heroGo.transform.localScale = new Vector3(25f, 25f, 25f);
                                heroGo.transform.localRotation = Quaternion.Euler(-90f, 0f, 0f);
                            }));

                            var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                            txtLevel.text = ToolScriptExtend.GetLangFormat(80000135, heroVo.level.ToString());
                            txtLevel.gameObject.SetActive(true);

                            var starNum = heroVo.StarNum;
                            var starOrder = heroVo.StarOrder;
                            for (int j = 0; j < starList.Count; j++)
                            {
                                var starSp = starList[j];
                                string pathStr;
                                if (j < starNum)
                                {
                                    pathStr = "Sprite/ui_hero/hero_icon_star5.png";
                                }
                                else if (j < starNum + 1 && starOrder > 0)
                                {
                                    pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder);
                                }
                                else
                                {
                                    pathStr = "Sprite/ui_hero/hero_icon_star0.png";
                                }

                                starSp.SetImage(pathStr);
                            }
                            starObj.gameObject.SetActive(true);
                        }
                        else
                        {
                            if (model.transform.childCount > 0)
                            {
                                Destroy(model.transform.GetChild(0).gameObject);
                            }
                        }
                    }
                    else
                    {
                        if (model.transform.childCount > 0)
                        {
                            Destroy(model.transform.GetChild(0).gameObject);
                        }
                    }
                }

                UIButton btn = item.GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    if (!isConductor) return;

                    bool teamIsUnlock = GameEntry.LogicData.BuildingData.GetTeamIsUnlock(index);
                    if (teamIsUnlock)
                    {
                        UITeamFormParam param = new()
                        {
                            Index = index - 1,
                            TeamFormType = UITeamFormType.TradeTrainDefend
                        };
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(1333),
                        });
                    }
                });
            }
        }

        void ResizeBg()
        {
            RectTransform rect = transform.GetComponent<RectTransform>();
            float rate = rect.rect.height / 1920f;
            m_rectBg.localScale = new Vector3(rate, rate, 1);
            float height = m_rectBg.rect.height * rate / 2;
            m_rectTrainGuard.sizeDelta = new Vector2(m_rectTrainGuard.rect.width, height);
            m_rectStationPlatform.sizeDelta = new Vector2(m_rectStationPlatform.rect.width, height);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_rectContent);
        }

        string GetBorder(PbGameconfig.car_quality quality)
        {
            return quality switch
            {
                PbGameconfig.car_quality._1 => "Sprite/ui_maoyi/maoyi_huoche_kapian1.png",
                PbGameconfig.car_quality._2 => "Sprite/ui_maoyi/maoyi_huoche_kapian2.png",
                PbGameconfig.car_quality._3 => "Sprite/ui_maoyi/maoyi_huoche_kapian3.png",
                PbGameconfig.car_quality._4 => "Sprite/ui_maoyi/maoyi_huoche_kapian4.png",
                PbGameconfig.car_quality._5 => "Sprite/ui_maoyi/maoyi_huoche_kapian5.png",
                _ => "Sprite/ui_maoyi/maoyi_huoche_kapian1.png",
            };
        }
        
        void RefreshBuff()
        {
            m_transBuffItem.gameObject.SetActive(false);
            m_goBuffTip.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
            m_btnBuffTip.gameObject.SetActive(false);
            m_goTip.SetActive(false);

            foreach (Transform item in m_transBuff)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < 4; i++)
            {
                Transform item;
                if (i < m_transBuff.childCount)
                {
                    item = m_transBuff.GetChild(i);
                }
                else
                {
                    item = Instantiate(m_transBuffItem, m_transBuff);
                }

                item.gameObject.SetActive(true);

                UIButton btn = item.GetComponent<UIButton>();
                UIImage icon = item.Find("icon").GetComponent<UIImage>();

                string key = $"{i + 1}_0";
                icon.SetImage(buffIcons[key]);

                int j = i;

                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    m_goBuffTip.SetActive(true);
                    m_btnMask.gameObject.SetActive(true);
                    m_btnBuffTip.gameObject.SetActive(false);
                    m_goTip.SetActive(false);
                    btn.AnchorUIToButton(m_goBuffTipArrow.transform, new Vector2(0, -89f));

                    if (j == 0)
                    {
                        m_txtBuffTipTitle.text = ToolScriptExtend.GetLang(80300009);
                        m_txtBuffTipContent.text = ToolScriptExtend.GetLang(80300017);
                    }
                    else if (j == 1)
                    {
                        m_txtBuffTipTitle.text = ToolScriptExtend.GetLang(80300011);
                        m_txtBuffTipContent.text = ToolScriptExtend.GetLang(80300019);
                    }
                    else if (j == 2)
                    {
                        m_txtBuffTipTitle.text = ToolScriptExtend.GetLang(80300013);
                        m_txtBuffTipContent.text = ToolScriptExtend.GetLang(80300021);
                    }
                    else if (j == 3)
                    {
                        string vip = ToolScriptExtend.GetLang(1304);
                        string str = $"<a href=\"\"><color=\"#f6630b\">{vip}</color></a>";
                        m_txtBuffTipTitle.text = ToolScriptExtend.GetLang(80300014);
                        m_txtBuffTipContent.text = ToolScriptExtend.GetLang(80300022);

                        string title = ToolScriptExtend.GetLang(1352);

                        m_txtBuffTipTitle.SetHyperlinkCallback((refValue, innerValue) =>
                        {
                            m_btnBuffTip.gameObject.SetActive(true);
                            m_goTip.SetActive(true);
                            m_txtTip.text = ToolScriptExtend.GetLang(1275);
                            int startIndex = title.Length - 3;
                            // m_txtBuffTipTitle.AnchorUIToTextSegment(startIndex, startIndex + vip.Length, m_goTip.transform, new Vector2(0, -20f));
                        });
                    }
                });
            }
        }
    }
}
