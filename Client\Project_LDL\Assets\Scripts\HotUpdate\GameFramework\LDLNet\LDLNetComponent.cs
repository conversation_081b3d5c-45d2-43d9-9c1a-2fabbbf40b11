
using System;
using System.Collections.Generic;
using Game.Hotfix.Proto;
using GameFramework;
using Gate;
using Google.Protobuf;
using LitJson;
using Network;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using UnityEngine;
using UnityGameFramework.Runtime;
using MessageID = Protocol.MessageID;
using Time = UnityEngine.Time;

namespace Game.Hotfix
{
    public class LDLNetComponent : GameFrameworkComponent
    {
        private static ushort _cid = 0; 
        public delegate void CallBackHandler(IMessage message);
        public delegate void CallBackErrorCodeHandler(ushort errorCode,IMessage message);
        
        private const float ReconnectDelayDefault = 5; // 默认重连延迟时
        private long gameConnID;
        private const float HeartInterval = 15;//心跳间隔

        private bool? _isConnecting;
        private bool? isConnecting
        {
            get
            {
                return _isConnecting;
            }
            set
            {
                if (value == true)
                {
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIConnectingForm))
                    {
                        GameEntry.UI.CloseUIForm(EnumUIForm.UIConnectingForm);
                    }
                }else if (value == false)
                {
                    if (_isConnecting == true && !GameEntry.UI.HasUIForm(EnumUIForm.UIConnectingForm))
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIConnectingForm);
                    }
                }
                _isConnecting = value;
            }
        }
        private float reconnectDelay = ReconnectDelayDefault;
        private float SendHeartElapsed = 0;

        private bool? m_IsReconnect = null;
        
        Dictionary<uint, CallBackErrorCodeHandler> callBackHandlers = new Dictionary<uint, CallBackErrorCodeHandler>();

        private SigninHttpRespServerInfo m_ServerInfo;
        
        public void Init()
        {
            ConnectSocket();
        }

        public void SetServerData(SigninHttpRespServerInfo serverInfo)
        {
            m_ServerInfo = serverInfo;
        }
        
        public bool IsConnecting()
        {
            return isConnecting == true;
        }
        
        public void DisconnectSocket()
        {
            if (gameConnID > 0)
            {
                NetworkEventMgr.Disconnect(gameConnID);
                gameConnID = 0;
            }
        }
        
        public void ConnectSocket(bool isReconnect = false)
        {
            m_IsReconnect = isReconnect;
            var nodes = m_ServerInfo.nodes;
            if (nodes.Count > 0)
            {
                var node = nodes[0];
                gameConnID = NetworkEventMgr.StartConnectSLG(node.host, node.port, OnSocketCallback);
            }
        }

        private void OnSocketCallback(int eventType, long connID, uint cid,ushort eCode,ushort protoID, byte[] data)
        {
            NetworkBaseEvent.NetworkEventType et = (NetworkBaseEvent.NetworkEventType)eventType;
            if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_ReceivedMessage)
            {
                HandleMessage(connID, cid, eCode, protoID, data);
            }
            else
            {
                if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectOK)
                {
                    OnConnectSuccess(connID);
                }
                else if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectFail)
                {
                    OnConnectFail(connID);
                }
                else if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectionError)
                {
                    OnConnectError(connID);
                }
                else if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectionLost)
                {
                    OnConnectLost(connID);
                }
                else if (et == NetworkBaseEvent.NetworkEventType.NetworkEvent_SocketClosed)
                {
                    OnConnectClose(connID);
                }
            }
        }

        private void HandleMessage(long connID, uint cid, ushort eCode, ushort protoID, byte[] data)
        {
            ushort errorCode = eCode;

            if (errorCode > 0)
            {
                var name = Enum.GetName(typeof(ResultCode), errorCode);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = "errorCode:" + errorCode + "  " + name + ToolScriptExtend.GetLang(errorCode)
                });
                ColorLog.Red("errorCode:" + errorCode);
            }
            
            Response message = Response.Descriptor.Parser.ParseFrom(data) as Response;
            if (message != null && message.Devmsg.Length > 0)
                ColorLog.Red("Response.Devmsg:" + message.Devmsg);
            
            var response = GetResponseProtoById(protoID, message.Payload);
            if (response != null)
            {
                //TODO
                TempConnectHelper.Instance.TryPrint(response, false, protoID);
                
                if (IsPushMsg(response,cid))
                {
                    HandlePushMessage(errorCode,protoID,response);
                }
                else
                {
                    //处理回调
                    CallBackErrorCodeHandler callBack = null;
                    if (callBackHandlers.TryGetValue(cid,out callBack))
                    {
                        callBack(errorCode,response);
                        callBackHandlers.Remove(cid);
                    }
                }
            }
        }

        public void Send(MessageID messageID, IMessage proto, CallBackErrorCodeHandler callBack = null)
        {
            Send((ushort)messageID, proto, callBack);
        }
        
        public void Send(MessageID messageID, IMessage proto, CallBackHandler callBack = null)
        {
            Send((ushort)messageID, proto, callBack);
        }
        
        private void Send(ushort protocolId,IMessage proto,System.Delegate callBack = null)
        {
            //TODO
            TempConnectHelper.Instance.TryPrint(proto, true, protocolId);

            byte[] bytes = proto != null ? proto.ToByteArray() : Array.Empty<byte>();
            Send(protocolId, bytes, callBack);
        }
        
        private void Send(ushort protocolId, byte[] bytes, System.Delegate callBack = null)
        {
            if (callBack!=null)
            {
                SendSync(protocolId, bytes, (ushort errorCode, IMessage response) =>
                {
                    if (response != null)
                    {
                        if (callBack is CallBackHandler callBackHandler)
                            callBackHandler(response);
                        else if (callBack is CallBackErrorCodeHandler callBackErrorCodeHandler)
                            callBackErrorCodeHandler(errorCode, response);
                    }
                });
            }
            else
            {
                SendSync(protocolId, bytes, (ushort errorCode, IMessage response) =>
                {

                });
            }
        }

        private void SendSync(ushort protocolId,byte[] data,CallBackErrorCodeHandler callBack)
        {
            var cid = GetNextCid();
            callBackHandlers[cid] = callBack;
            NetworkEventMgr.SendMessage(gameConnID,cid,protocolId,data);
        }
        
        private void OnConnectSuccess(long connID)
        {
            isConnecting = true;
            SendHeart();

            if (m_IsReconnect == true)
            {
                LoginAgain(() =>
                {
                    m_IsReconnect = null;
                });
            }
        }

        private void OnConnectFail(long connID)
        {
            isConnecting = false;
            DisconnectSocket();
        }

        private void OnConnectError(long connID)
        {
            isConnecting = false;
        }

        private void OnConnectLost(long connID)
        {
            isConnecting = false;
            DisconnectSocket();
        }

        private void OnConnectClose(long connID)
        {
            isConnecting = false;
        }

        /// <summary>
        /// 重连 TODO 数据刷新未处理
        /// </summary>
        /// <param name="callBack"></param>
        private void LoginAgain(Action callBack)
        {
            var userData = GameEntry.LogicData.UserData;
            //请求初始化数据
            LoginReq loginReq = new LoginReq();
            loginReq.Token = userData.access_token;
            loginReq.Uuid = (ulong)userData.uuid;
            loginReq.ServerId = (uint)m_ServerInfo.id;
            loginReq.Version = ProcedureLogin.GetTestVersion();
            
            GameEntry.LDLNet.Send(MessageID.Login,loginReq, (message) =>
            {
                callBack?.Invoke();
            });
        }
        
        public void Update()
        {
            if (isConnecting.HasValue && !isConnecting.Value)
            {
                if (reconnectDelay > 0)
                {
                    
                    reconnectDelay -= Time.deltaTime;
                }
                else
                {
                    reconnectDelay = ReconnectDelayDefault;
                    isConnecting = null;
                    ConnectSocket(true);
                }
            }

            if (isConnecting.HasValue && isConnecting.Value)
            {
                SendHeartElapsed += Time.deltaTime;
                if (SendHeartElapsed > HeartInterval)
                {
                    SendHeartElapsed = 0;
                    SendHeart();
                }
            }
        }

        private bool IsPushMsg(IMessage proto, uint cid)
        {
            if (cid > 0)
            {
                return false;
            }

            return true;
        }
        
        private void HandlePushMessage(ushort errorCode,int protoID,IMessage message)
        {
            if (errorCode > 0)
            {
                //TODO 错误提示
            }
            else
            {
                NetEventDispatch.Instance.PostEvent(protoID, message);
            }
        }

        private uint GetNextCid()
        {
            return ++_cid;
        }
        
        private IMessage GetResponseProtoById(int id,ByteString data)
        {
            var messageDefine = GameEntry.LDLTable.GetMessageDefineById(id);
            if (messageDefine != null)
            {
                if (messageDefine.Response != null)
                {
                    string responseClassName = messageDefine.Response;
                    // if (messageDefine.PkgName.Length > 0 && !responseClassName.StartsWith(messageDefine.PkgName))
                    // {
                    //     responseClassName = messageDefine.PkgName + "." + responseClassName;
                    // }
                    Type responseType = Type.GetType(responseClassName);
                    if (responseType != null)
                    {
                        object responseObject = Activator.CreateInstance(responseType);
                        IMessage typedResponse = responseObject as IMessage;
                        if (typedResponse != null)
                        {
                            var message = typedResponse.Descriptor.Parser.ParseFrom(data);
                            if (message != null)
                            {
                                return message;
                            }
                            else
                            {
                                Debug.LogError(
                                    $"Protocol deserialization failed.id:'{id}'  responseClassName:'{responseClassName}'");
                            }
                        }
                        else
                        {
                            Debug.LogError($"Error: Could not cast responseObject to '{responseClassName}'");
                        }
                    }
                    else
                    {
                        Debug.LogError($"Error: Type '{responseClassName}' not found.  Make sure the assembly is loaded and the class exists.");
                    }
                }
            }
            else
            {
                Debug.LogError($"Error: Could not find messageDefine by id '{id}'");
            }

            return null;

            // Google.Protobuf.IMessage call = new Gate.HeartResp();
            // Gate.HeartResp heartResp = (Gate.HeartResp)call.Descriptor.Parser.ParseFrom(Google.Protobuf.ByteString.CopyFrom(data));
            // return heartResp;
        }

        public void SendHeart()
        {
            Send(MessageID.Heart,null, (message) =>
            {
                var proto = message as HeartResp;
                if (proto != null)
                    GameEntry.Time.SetServerTime(proto);
            });
        }
        
        private void SendTestData()
        {
            // string test = "1";
            // MessageID.SecretSharePubKey
            // var t = new SecretSharePubKeyReq();
            // t.PubKey = "ABB";
            // t.
            // Send(1,t, (IMessage message) =>
            // {
            //     // HeartResp.Descriptor
            //     // Debug.LogError("");
            // });
            
            // Send(1,Encoding.UTF8.GetBytes(test), (message) =>
            // {
            //     LogMan.Debug("11111");
            // });
        }
        
        //获取开服时间戳
        public int GetOpenServiceTimestamp()
        {
            var startTimestamp = m_ServerInfo.openAt;//开服时间戳
            if (startTimestamp == 0)
            {
                startTimestamp = 1743436800;//临时数据测试用  2025-04-01 00:00:00
            }
            return startTimestamp;
        }
        
        //获取开服天数
        public int GetOpenServiceDays()
        {
            var startTimestamp = GetOpenServiceTimestamp();//开服时间戳
            var time = (int)TimeComponent.Now - startTimestamp;
            var span = new TimeSpan(0,0,time);
            return span.Days;
        }
    }
}