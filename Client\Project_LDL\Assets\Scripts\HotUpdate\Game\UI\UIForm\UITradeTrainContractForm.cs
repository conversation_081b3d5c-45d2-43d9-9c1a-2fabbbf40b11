using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using GameFramework.Event;

namespace Game.Hotfix
{
    public partial class UITradeTrainContractForm : UGuiFormEx
    {
        readonly int paymentID = 11101002;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            RefreshPanel();

            GameEntry.Event.Subscribe(PaymentFinishEventArgs.EventId, OnPaymentFinishEventArgs);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(PaymentFinishEventArgs.EventId, OnPaymentFinishEventArgs);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnPaymentFinishEventArgs(object sender, GameEventArgs e)
        {
            if (e is PaymentFinishEventArgs args)
            {

            }

            Close();
        }

        private void OnBtnCloseFullClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainProbabilityForm);
        }

        private void OnBtnBuyClick()
        {
            GameEntry.PaymentData.Pay(11101002);
        }

        void RefreshPanel()
        {
            gift_pack giftPack = GameEntry.LDLTable.GetTableById<gift_pack>(paymentID);
            if (giftPack != null)
            {
                RefreshReward(giftPack.gift_pack_reward);
            }

            payment payment = GameEntry.LDLTable.GetTableById<payment>(paymentID);
            if (payment != null)
            {
                m_txtPrice.text = ToolScriptExtend.GetLang(payment.price_lang_id);
            }

            m_txtTodayCount.text = ToolScriptExtend.GetLang(1293) + $"{GameEntry.TradeTruckData.BuyExpressContractTimes}/2";
        }

        void RefreshReward(List<reward> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData(rewards[i].item_id, rewards[i].num);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, rewards[i].item_id, rewards[i].num, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.8f);
                    });
                }
            }
        }
    }
}
