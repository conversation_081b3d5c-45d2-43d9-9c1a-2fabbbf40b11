using GameFramework.DataTable;
using GameFramework.Event;
using GameFramework.Fsm;
using GameFramework.Procedure;
using Gate;
using Protocol;
using Role;
using Roledata;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ProcedureLogin : ProcedureBase
    {
        private IFsm<IProcedureManager> m_ProcedureOwner;
        private int? m_LoginFormSerialId;
        private Scene? m_EmptyScene;
        protected override void OnEnter(IFsm<IProcedureManager> procedureOwner)
        {
            base.OnEnter(procedureOwner);
            m_ProcedureOwner = procedureOwner;
            
            if (TempConnectHelper.Instance.GetPath() != null)
            {
                var param = new UILoginFormParam();
                param.ProcedureLogin = this;
                
                m_LoginFormSerialId = GameEntry.UI.OpenUIForm(EnumUIForm.UILoginForm, param);

                GameEntry.UI.OpenUIForm(EnumUIForm.UIClickEffectForm);
            }
            else if (TempConnectHelper.Instance.ConnectTargetPath == TempConnectHelper.ConnectTarget.技能编辑)
            {
                GetNext(3);
            }
            else
            {
                GameEntry.LogicData.InitGameData(null);
                GetNext();
            }
        }

        public void OnConnectSuccess(SigninHttpRespServerInfo serverInfo)
        {
            var userData = GameEntry.LogicData.UserData;
            //请求初始化数据
            LoginReq loginReq = new LoginReq();
            loginReq.Token = userData.access_token;
            loginReq.Uuid = (ulong)userData.uuid;
            loginReq.ServerId = (uint)serverInfo.id;
            loginReq.Version = GetTestVersion();
            
            GameEntry.LDLNet.Send(MessageID.Login,loginReq, (message) =>
            {
                LoginResp resp = (LoginResp)message;
                GameEntry.LogicData.InitGameData(resp.Role);
                userData.temp_purchase_url = serverInfo.temp_purchase_url;
                GetNext();
            });
        }

        // public void SelectUserAndGetInfo(RoleBrief roleBrief)
        // {
        //     RoleEnterReq roleEnterReq = new RoleEnterReq();
        //     roleEnterReq.RoleId = roleBrief.Id;
        //     GameEntry.LDLNet.Send(MessageID.RoleEnter,roleEnterReq, (message) =>
        //     {
        //         RoleEnterResp roleEnterResp = (RoleEnterResp)message;
        //         if (roleEnterResp != null)
        //         {
        //             GameEntry.LogicData.InitGameData(roleEnterResp);
        //             GetNext();
        //         }
        //     });
        //         
        // }
        
        public static Version GetTestVersion()
        {
            var version = new Version();
            version.ChatVersionMax = 1;
            version.ChatVersionMin = 1;
            version.GameVersionMax = 1;
            version.GameVersionMin = 1;
            version.MailVersionMax = 1;
            version.MailVersionMin = 1;
            return version;
        }

        private AsyncOperation asyncUnload;
        private void GetNext(int sceneId = 2)
        {
        //    m_EmptyScene ??= SceneManager.CreateScene(GameDefine.EmptySceneName);
            
        //     var launchScene = SceneManager.GetSceneByName("LauncherScene");
        //     if (launchScene is { isLoaded: true })
        //         {
        //             AsyncOperation asyncOperation = SceneManager.UnloadSceneAsync(launchScene);
        //         }
            
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId",sceneId);//GameEntry.Config.GetInt("Scene.Menu"));
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }
        
        protected override void OnLeave(IFsm<IProcedureManager> procedureOwner, bool isShutdown)
        {
            base.OnLeave(procedureOwner, isShutdown);
            
            if (m_LoginFormSerialId != null)
            {
                GameEntry.UI.CloseUIForm(m_LoginFormSerialId.Value);
            }
        }

        protected override void OnUpdate(IFsm<IProcedureManager> procedureOwner, float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(procedureOwner, elapseSeconds, realElapseSeconds);

            
            
        }

        public override bool UseNativeDialog { get; }
    }
}
