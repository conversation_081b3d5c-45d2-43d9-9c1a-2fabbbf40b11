using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTruckHistoryForm : UGuiFormEx
    {
        Trade.TradeVanRecordListResp plunderData;
        Trade.TradeVanRecordListResp departData;
        Trade.TradeVanRecordListResp collectData;
        List<Trade.TradeVanRobRecord> plunderRecords = new();
        List<Trade.TradeVanDepartRecord> departRecords = new();
        List<Trade.TradeVanCollectRecord> collectRecords = new();

        ScrollRect scrollRect;
        bool isLoading;
        int plunderRecordPage;
        int plunderRecordCount;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPlunder();
            InitDepart();
            InitCollect();

            m_togPlunder.onValueChanged.AddListener(OnTogPlunder);
            m_togDepart.onValueChanged.AddListener(OnTogDepart);
            m_togCollect.onValueChanged.AddListener(OnTogCollect);

            scrollRect = m_TableViewVPlunder.GetComponent<ScrollRect>();
            scrollRect.onValueChanged.AddListener((pos) =>
            {
                // 如果滑动到接近底部，且当前没有正在加载数据
                if (pos.y <= 0f && !isLoading)
                {
                    isLoading = true;
                    plunderRecordPage++;
                    plunderRecordCount = plunderRecords.Count;
                    RequestPlunderRecord(plunderRecordPage);
                }
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            plunderRecords.Clear();
            plunderRecordPage = 0;
            plunderRecordCount = 0;
            scrollRect.verticalNormalizedPosition = 1f;
            m_togPlunder.isOn = true;
            OnTogPlunder(true);
            RequestPlunderRecord();
            m_goNoData.SetActive(plunderRecords.Count == 0);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            if (userData is TruckHistoryRefreshType type)
            {
                switch (type)
                {
                    case TruckHistoryRefreshType.PlunderRefresh:
                        RequestPlunderRecord();
                        break;
                    case TruckHistoryRefreshType.DepartRefresh:
                        RequestDepartRecord();
                        break;
                    case TruckHistoryRefreshType.CollectRefresh:
                        RequestCollectRecord();
                        break;
                }
            }
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        void ShowNoData()
        {
            if (m_togPlunder.isOn)
            {
                m_goNoData.SetActive(plunderRecords.Count == 0);
            }
            else if (m_togDepart.isOn)
            {
                m_goNoData.SetActive(departRecords.Count == 0);
            }
            else if (m_togCollect.isOn)
            {
                m_goNoData.SetActive(collectRecords.Count == 0);
            }
        }

        void OnTogPlunder(bool isOn)
        {
            m_TableViewVPlunder.gameObject.SetActive(isOn);
            ShowNoData();
        }

        void RequestPlunderRecord(int page = 0)
        {
            GameEntry.TradeTruckData.RequestTruckRecord(Trade.TradeRecordType.Rob, (result) =>
            {
                ColorLog.Pink("货车掠夺记录", result);
                plunderData = result;
                List<Trade.TradeVanRobRecord> newRecords = new(plunderData.RobRecord);
                plunderRecords.AddRange(newRecords);
                plunderRecords.Sort((a, b) => b.Id.CompareTo(a.Id));

                if (m_TableViewVPlunder.itemPrototype == null)
                {
                    m_TableViewVPlunder.InitTableViewByIndex(0, 12);
                }
                else
                {
                    m_TableViewVPlunder.ReloadData();
                }

                ShowNoData();

                if (plunderRecordCount != plunderRecords.Count)
                {
                    isLoading = false;
                }
            }, page);
        }

        void OnTogDepart(bool isOn)
        {
            m_TableViewVDepart.gameObject.SetActive(isOn);

            if (isOn)
            {
                RequestDepartRecord();
            }
        }

        void RequestDepartRecord()
        {
            GameEntry.TradeTruckData.RequestTruckRecord(Trade.TradeRecordType.Depart, (result) =>
            {
                ColorLog.Pink("货车发车记录", result);
                departData = result;
                departRecords = new(departData.DepartRecord);
                departRecords.Sort((a, b) => b.Record.Id.CompareTo(a.Record.Id));

                if (m_TableViewVDepart.itemPrototype == null)
                {
                    m_TableViewVDepart.InitTableViewByIndex(0, 12);
                }
                else
                {
                    m_TableViewVDepart.ReloadData();
                }

                ShowNoData();
            });
        }

        void OnTogCollect(bool isOn)
        {
            m_TableViewVCollect.gameObject.SetActive(isOn);

            if (isOn)
            {
                RequestCollectRecord();
            }
        }

        void RequestCollectRecord()
        {
            GameEntry.TradeTruckData.RequestTruckRecord(Trade.TradeRecordType.Collect, (result) =>
            {
                ColorLog.Pink("货车收藏记录", result);
                collectData = result;
                collectRecords = new(collectData.CollectRecord);
                collectRecords.Sort((a, b) => b.Record.Id.CompareTo(a.Record.Id));

                if (m_TableViewVCollect.itemPrototype == null)
                {
                    m_TableViewVCollect.InitTableViewByIndex(0, 12);
                }
                else
                {
                    m_TableViewVCollect.ReloadData();
                }

                ShowNoData();
            });
        }

        void InitPlunder()
        {
            m_TableViewVPlunder.GetItemCount = () =>
            {
                if (plunderRecords != null)
                {
                    return plunderRecords.Count;
                }
                return 0;
            };
            m_TableViewVPlunder.GetItemGo = () => m_goPlunderItem;
            m_TableViewVPlunder.UpdateItemCell = UpdatePlunderView;
            m_TableViewVPlunder.InitTableViewByIndex(0, 12);
            m_TableViewVDepart.gameObject.SetActive(true);
        }

        void UpdatePlunderView(int index, GameObject obj)
        {
            if (plunderRecords == null) return;
            if (index >= plunderRecords.Count) return;

            UIText txtResult = obj.transform.Find("txtResult").GetComponent<UIText>();
            UIText txtTime = obj.transform.Find("txtTime").GetComponent<UIText>();
            UIText txtDetail = obj.transform.Find("txtDetail").GetComponent<UIText>();
            UIImage imgResult = obj.transform.Find("imgResult").GetComponent<UIImage>();

            Trade.TradeVanRobRecord data = plunderRecords[index];
            bool isSelf = data.RoleId == GameEntry.RoleData.RoleID;
            if (!isSelf && data.Type == Battle.BattleResult.AttackerWin)
            {
                txtResult.text = ToolScriptExtend.GetLang(1315);
                ColorUtility.TryParseHtmlString("#007905", out Color color);
                txtResult.color = color;
                string str = "#994 [DLYM]Downsouth0021";
                txtDetail.text = ToolScriptExtend.GetLangFormat(1319, str);
                imgResult.SetImage("Sprite/ui_youjian/youjian_main2_icon7.png");
            }
            else if (!isSelf && data.Type == Battle.BattleResult.DefenderWin)
            {
                txtResult.text = ToolScriptExtend.GetLang(1316);
                ColorUtility.TryParseHtmlString("#c80a00", out Color color);
                txtResult.color = color;
                string str = "#994 [DLYM]Downsouth0021";
                txtDetail.text = ToolScriptExtend.GetLangFormat(1133, str);
                imgResult.SetImage("Sprite/ui_youjian/youjian_main2_icon8.png");
            }
            else if (isSelf && data.Type == Battle.BattleResult.DefenderWin)
            {
                txtResult.text = ToolScriptExtend.GetLang(1317);
                ColorUtility.TryParseHtmlString("#007905", out Color color);
                txtResult.color = color;
                string str = "#994 [DLYM]Downsouth0021";
                txtDetail.text = ToolScriptExtend.GetLangFormat(1319, str);
                imgResult.SetImage("Sprite/ui_youjian/youjian_main2_icon7.png");
            }
            else if (isSelf && data.Type == Battle.BattleResult.AttackerWin)
            {
                txtResult.text = ToolScriptExtend.GetLang(1318);
                ColorUtility.TryParseHtmlString("#c80a00", out Color color);
                txtResult.color = color;
                string str = "#994 [DLYM]Downsouth0021";
                txtDetail.text = ToolScriptExtend.GetLangFormat(1133, str);
                imgResult.SetImage("Sprite/ui_youjian/youjian_main2_icon8.png");
            }
            txtTime.text = TimeComponent.FormatMillisecondsToTimeAgo(data.Id);

            UIButton button = obj.GetComponent<UIButton>();
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() =>
            {
                MailDetailInfo mailDetailInfo = new()
                {
                    mailId = 1,
                    contentType = MailContentType.TradeTruck,
                    content = string.Empty
                };
                GameEntry.UI.OpenUIForm(EnumUIForm.UIMailDetailForm, mailDetailInfo);
            });
        }

        void InitDepart()
        {
            m_TableViewVDepart.GetItemCount = () =>
            {
                if (departRecords != null)
                {
                    return departRecords.Count;
                }
                return 0;
            };
            m_TableViewVDepart.GetItemGo = () => m_goDepartItem;
            m_TableViewVDepart.UpdateItemCell = UpdateDepartView;
            m_TableViewVDepart.InitTableViewByIndex(0, 12);
            m_TableViewVDepart.gameObject.SetActive(false);
        }

        void UpdateDepartView(int index, GameObject obj)
        {
            if (departRecords == null) return;
            if (index >= departRecords.Count) return;

            UIImage imgTitle = obj.transform.Find("bg/imgTitle").GetComponent<UIImage>();
            UIImage imgQuality = obj.transform.Find("bg/imgTitle/imgQuality").GetComponent<UIImage>();
            UIImage imgTruckIcon = obj.transform.Find("bg/imgTruckIcon").GetComponent<UIImage>();
            Transform transContentReward = obj.transform.Find("bg/Scroll View/Viewport/transContentReward");
            Transform transReward = obj.transform.Find("bg/transReward");
            UIText txtTime = obj.transform.Find("bg/imgTitle/txtTime").GetComponent<UIText>();

            Trade.TradeVanRecord data = departRecords[index].Record;
            trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(data.TradeId);
            car_quality quality = truckConfig.car_quality;
            imgTitle.SetImage(GetQualityTitle(quality));
            imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
            imgTruckIcon.SetImage(GetTruckIcon(quality));
            if (data.Goods.Count > 0)
            {
                List<Trade.TradeGoods> rewards = new(data.Goods);
                RefreshReward(rewards, transContentReward, transReward);
            }
            txtTime.text = TimeHelper.FormatMillisecondsToDateTime(data.Id);

            UIButton button = obj.GetComponent<UIButton>();
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() =>
            {
                ColorLog.Pink("货车详情", data);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDetailForm, new UITradeTruckDetailFormParams()
                {
                    type = TruckDetailType.History,
                    truckRecord = data
                });
            });
        }

        void InitCollect()
        {
            m_TableViewVCollect.GetItemCount = () =>
            {
                if (collectRecords != null)
                {
                    return collectRecords.Count;
                }
                return 0;
            };
            m_TableViewVCollect.GetItemGo = () => m_goDepartItem;
            m_TableViewVCollect.UpdateItemCell = UpdateCollectView;
            m_TableViewVCollect.InitTableViewByIndex(0, 12);
            m_TableViewVCollect.gameObject.SetActive(false);
        }

        void UpdateCollectView(int index, GameObject obj)
        {
            if (collectRecords == null) return;
            if (index >= collectRecords.Count) return;

            UIImage imgTitle = obj.transform.Find("bg/imgTitle").GetComponent<UIImage>();
            UIImage imgQuality = obj.transform.Find("bg/imgTitle/imgQuality").GetComponent<UIImage>();
            UIImage imgTruckIcon = obj.transform.Find("bg/imgTruckIcon").GetComponent<UIImage>();
            Transform transContentReward = obj.transform.Find("bg/Scroll View/Viewport/transContentReward");
            Transform transReward = obj.transform.Find("bg/transReward");
            UIText txtTime = obj.transform.Find("bg/imgTitle/txtTime").GetComponent<UIText>();

            Trade.TradeVanRecord data = collectRecords[index].Record;
            trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(data.TradeId);
            car_quality quality = truckConfig.car_quality;
            imgTitle.SetImage(GetQualityTitle(quality));
            imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
            imgTruckIcon.SetImage(GetTruckIcon(quality));
            if (data.Goods.Count > 0)
            {
                List<Trade.TradeGoods> rewards = new(data.Goods);
                RefreshReward(rewards, transContentReward, transReward);
            }
            txtTime.text = TimeHelper.FormatMillisecondsToDateTime(data.Id);

            UIButton button = obj.GetComponent<UIButton>();
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() =>
            {
                ColorLog.Pink("货车详情", data);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDetailForm, new UITradeTruckDetailFormParams()
                {
                    type = TruckDetailType.History,
                    truckRecord = data
                });
            });
        }

        void RefreshReward(List<Trade.TradeGoods> rewards, Transform transContentReward, Transform transReward)
        {
            foreach (Transform item in transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            rewards.Sort((a, b) => a.Rob.CompareTo(b.Rob));

            for (int i = 0; i < rewards.Count; i++)
            {
                bool rob = rewards[i].Rob;
                if (i < transContentReward.childCount)
                {
                    transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                    uiItemModule.plunder.SetActive(rob);
                }
                else
                {
                    Transform item = Instantiate(transReward, transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.55f);
                        item.plunder.SetActive(rob);
                    });
                }
            }
        }

        string GetQualityTitle(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_dis_dikuang1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_dis_dikuang2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_dis_dikuang3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_dis_dikuang4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_dis_dikuang5.png",
                _ => "Sprite/ui_maoyi/maoyi_dis_dikuang1.png",
            };
        }

        string GetTruckIcon(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_dis_car1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_dis_car2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_dis_car3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_dis_car4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_dis_car5.png",
                _ => "Sprite/ui_maoyi/maoyi_dis_car1.png",
            };
        }
    }
}
